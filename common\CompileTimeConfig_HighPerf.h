#pragma once

// High Performance Profile Configuration
// Optimized for gaming and real-time applications
// - Maximum speed, lower quality
// - High frame rate for responsiveness

// JPEG compression quality (1-100, lower = faster compression, lower quality)
#define HVNC_JPEG_QUALITY 35

// Frame rate limit (frames per second)
#define HVNC_FRAME_RATE_LIMIT 60

// Capture interval in milliseconds (calculated from frame rate)
#define HVNC_CAPTURE_INTERVAL_MS (1000 / HVNC_FRAME_RATE_LIMIT)

// Differential capture threshold (0-255, lower = more sensitive to changes)
#define HVNC_PIXEL_DIFF_THRESHOLD 15

// LZNT1 compression level (1-12, higher = better compression, slower)
#define HVNC_COMPRESSION_LEVEL 4

// Profile information for logging
#define HVNC_PROFILE_NAME "High Performance"
#define HVNC_PROFILE_DESCRIPTION "Gaming/Real-time optimized"

// Logging macro for profile information
#define HVNC_LOG_PROFILE_INFO() \
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, \
        "HVNC Profile: %s | JPEG Quality: %d | Frame Rate: %d FPS | Compression: %d", \
        HVNC_PROFILE_NAME, HVNC_JPEG_QUALITY, HVNC_FRAME_RATE_LIMIT, HVNC_COMPRESSION_LEVEL)
