#pragma once

// Define a default profile if none is specified
#if !defined(HVNC_HIGH_QUALITY) && !defined(HVNC_BALANCED) && !defined(HVNC_HIGH_PERFORMANCE)
#define HVNC_HIGH_QUALITY
#endif

// Build profile selection via preprocessor definitions:
#if defined(HVNC_HIGH_QUALITY)
// High Quality Profile Configuration
// Optimized for detailed visual tasks, slower speed
#define HVNC_JPEG_QUALITY       90
#define HVNC_FRAME_RATE_LIMIT   30
#define HVNC_CAPTURE_INTERVAL_MS (1000 / HVNC_FRAME_RATE_LIMIT)
#define HVNC_PIXEL_DIFF_THRESHOLD 25
#define HVNC_COMPRESSION_LEVEL  8
#define HVNC_PROFILE_NAME       "High Quality"

#elif defined(HVNC_BALANCED)
// Balanced Profile Configuration
// Medium quality vs. performance trade-off
#define HVNC_JPEG_QUALITY       60
#define HVNC_FRAME_RATE_LIMIT   45
#define HVNC_CAPTURE_INTERVAL_MS (1000 / HVNC_FRAME_RATE_LIMIT)
#define HVNC_PIXEL_DIFF_THRESHOLD 16
#define HVNC_COMPRESSION_LEVEL  6
#define HVNC_PROFILE_NAME       "Balanced"

#elif defined(HVNC_HIGH_PERFORMANCE)
// High Performance Profile Configuration
// Lower quality for maximum speed
#define HVNC_JPEG_QUALITY       35
#define HVNC_FRAME_RATE_LIMIT   60
#define HVNC_CAPTURE_INTERVAL_MS (1000 / HVNC_FRAME_RATE_LIMIT)
#define HVNC_PIXEL_DIFF_THRESHOLD 8
#define HVNC_COMPRESSION_LEVEL  4
#define HVNC_PROFILE_NAME       "High Performance"

#else
#error "No HVNC profile defined. Define HVNC_HIGH_QUALITY, HVNC_BALANCED, or HVNC_HIGH_PERFORMANCE in project preprocessor settings."
#endif

// Logging macro for profile information
#define HVNC_LOG_PROFILE_INFO() \
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, \
        "HVNC Profile: %s | JPEG Quality: %d | Frame Rate: %d FPS | Compression: %d", \
        HVNC_PROFILE_NAME, HVNC_JPEG_QUALITY, HVNC_FRAME_RATE_LIMIT, HVNC_COMPRESSION_LEVEL)
