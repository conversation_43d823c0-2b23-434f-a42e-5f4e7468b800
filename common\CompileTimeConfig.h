#pragma once

/*
 * Compile-Time Performance Configuration for HVNC
 * 
 * This header defines three performance profiles that are selected at compile time.
 * No runtime performance adjustment is possible - all settings are hardcoded constants.
 * 
 * Profiles:
 * - HIGH_PERFORMANCE: Maximum speed, lower quality
 * - BALANCED: Moderate speed and quality  
 * - HIGH_QUALITY: Best quality, slower speed
 */

// Performance profile selection (only one should be defined)
// These are set by the build system via preprocessor definitions

#if defined(HVNC_HIGH_PERFORMANCE)
    // HIGH PERFORMANCE PROFILE
    // Optimized for maximum speed and responsiveness
    #define HVNC_PROFILE_NAME "High Performance"
    
    // JPEG compression settings
    #define HVNC_JPEG_QUALITY           35
    #define HVNC_JPEG_SUBSAMPLING       TJSAMP_420  // 4:2:0 for best compression speed
    #define HVNC_JPEG_FLAGS             TJFLAG_FASTDCT
    
    // Capture settings
    #define HVNC_FRAME_RATE_LIMIT       60
    #define HVNC_CAPTURE_INTERVAL_MS    16   // ~60 FPS
    #define HVNC_SKIP_FRAME_THRESHOLD   2    // Skip frames aggressively
    
    // Differential capture thresholds
    #define HVNC_PIXEL_DIFF_THRESHOLD   25   // Lower threshold = more sensitive
    #define HVNC_REGION_MIN_SIZE        64   // Smaller regions for faster processing
    #define HVNC_REGION_MAX_SIZE        2048
    
    // Compression settings
    #define HVNC_LZNT1_COMPRESSION      TRUE
    #define HVNC_COMPRESSION_LEVEL      3    // Fast compression
    #define HVNC_COMPRESSION_THRESHOLD  512  // Compress smaller blocks
    
    // Network settings
    #define HVNC_SEND_BUFFER_SIZE       65536
    #define HVNC_RECEIVE_BUFFER_SIZE    65536
    #define HVNC_NETWORK_TIMEOUT        1000
    
    // Image processing
    #define HVNC_STRETCH_MODE           COLORONCOLOR  // Fast scaling
    #define HVNC_USE_HARDWARE_ACCEL     TRUE
    #define HVNC_ADAPTIVE_QUALITY       FALSE

#elif defined(HVNC_BALANCED)
    // BALANCED PROFILE
    // Good balance between speed and quality
    #define HVNC_PROFILE_NAME "Balanced"
    
    // JPEG compression settings
    #define HVNC_JPEG_QUALITY           65
    #define HVNC_JPEG_SUBSAMPLING       TJSAMP_420
    #define HVNC_JPEG_FLAGS             TJFLAG_FASTDCT
    
    // Capture settings
    #define HVNC_FRAME_RATE_LIMIT       45
    #define HVNC_CAPTURE_INTERVAL_MS    22   // ~45 FPS
    #define HVNC_SKIP_FRAME_THRESHOLD   3
    
    // Differential capture thresholds
    #define HVNC_PIXEL_DIFF_THRESHOLD   20
    #define HVNC_REGION_MIN_SIZE        128
    #define HVNC_REGION_MAX_SIZE        4096
    
    // Compression settings
    #define HVNC_LZNT1_COMPRESSION      TRUE
    #define HVNC_COMPRESSION_LEVEL      6    // Balanced compression
    #define HVNC_COMPRESSION_THRESHOLD  1024
    
    // Network settings
    #define HVNC_SEND_BUFFER_SIZE       32768
    #define HVNC_RECEIVE_BUFFER_SIZE    32768
    #define HVNC_NETWORK_TIMEOUT        2000
    
    // Image processing
    #define HVNC_STRETCH_MODE           HALFTONE      // Better quality scaling
    #define HVNC_USE_HARDWARE_ACCEL     FALSE
    #define HVNC_ADAPTIVE_QUALITY       FALSE

#elif defined(HVNC_HIGH_QUALITY)
    // HIGH QUALITY PROFILE
    // Optimized for best visual quality
    #define HVNC_PROFILE_NAME "High Quality"
    
    // JPEG compression settings
    #define HVNC_JPEG_QUALITY           90
    #define HVNC_JPEG_SUBSAMPLING       TJSAMP_444    // 4:4:4 for best quality
    #define HVNC_JPEG_FLAGS             TJFLAG_ACCURATEDCT
    
    // Capture settings
    #define HVNC_FRAME_RATE_LIMIT       30
    #define HVNC_CAPTURE_INTERVAL_MS    33   // ~30 FPS
    #define HVNC_SKIP_FRAME_THRESHOLD   5    // Less aggressive frame skipping
    
    // Differential capture thresholds
    #define HVNC_PIXEL_DIFF_THRESHOLD   15   // Higher threshold = less sensitive
    #define HVNC_REGION_MIN_SIZE        256  // Larger regions for better quality
    #define HVNC_REGION_MAX_SIZE        8192
    
    // Compression settings
    #define HVNC_LZNT1_COMPRESSION      TRUE
    #define HVNC_COMPRESSION_LEVEL      9    // Maximum compression
    #define HVNC_COMPRESSION_THRESHOLD  2048
    
    // Network settings
    #define HVNC_SEND_BUFFER_SIZE       16384
    #define HVNC_RECEIVE_BUFFER_SIZE    16384
    #define HVNC_NETWORK_TIMEOUT        5000
    
    // Image processing
    #define HVNC_STRETCH_MODE           HALFTONE
    #define HVNC_USE_HARDWARE_ACCEL     FALSE
    #define HVNC_ADAPTIVE_QUALITY       FALSE

#else
    // DEFAULT TO BALANCED PROFILE if no profile is specified
    #define HVNC_BALANCED
    #define HVNC_PROFILE_NAME "Balanced (Default)"
    
    #define HVNC_JPEG_QUALITY           65
    #define HVNC_JPEG_SUBSAMPLING       TJSAMP_420
    #define HVNC_JPEG_FLAGS             TJFLAG_FASTDCT
    #define HVNC_FRAME_RATE_LIMIT       45
    #define HVNC_CAPTURE_INTERVAL_MS    22
    #define HVNC_SKIP_FRAME_THRESHOLD   3
    #define HVNC_PIXEL_DIFF_THRESHOLD   20
    #define HVNC_REGION_MIN_SIZE        128
    #define HVNC_REGION_MAX_SIZE        4096
    #define HVNC_LZNT1_COMPRESSION      TRUE
    #define HVNC_COMPRESSION_LEVEL      6
    #define HVNC_COMPRESSION_THRESHOLD  1024
    #define HVNC_SEND_BUFFER_SIZE       32768
    #define HVNC_RECEIVE_BUFFER_SIZE    32768
    #define HVNC_NETWORK_TIMEOUT        2000
    #define HVNC_STRETCH_MODE           HALFTONE
    #define HVNC_USE_HARDWARE_ACCEL     FALSE
    #define HVNC_ADAPTIVE_QUALITY       FALSE
#endif

// Compile-time validation
#if HVNC_JPEG_QUALITY < 1 || HVNC_JPEG_QUALITY > 100
    #error "HVNC_JPEG_QUALITY must be between 1 and 100"
#endif

#if HVNC_FRAME_RATE_LIMIT < 1 || HVNC_FRAME_RATE_LIMIT > 120
    #error "HVNC_FRAME_RATE_LIMIT must be between 1 and 120"
#endif

#if HVNC_COMPRESSION_LEVEL < 1 || HVNC_COMPRESSION_LEVEL > 9
    #error "HVNC_COMPRESSION_LEVEL must be between 1 and 9"
#endif

// Utility macros for logging the current profile
#define HVNC_LOG_PROFILE_INFO() \
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, \
        "HVNC Profile: %s | JPEG Quality: %d | Frame Rate: %d FPS | Compression: %d", \
        HVNC_PROFILE_NAME, HVNC_JPEG_QUALITY, HVNC_FRAME_RATE_LIMIT, HVNC_COMPRESSION_LEVEL)

// Legacy compatibility - these constants replace runtime variables
#define g_currentJpegQuality        HVNC_JPEG_QUALITY
#define CAPTURE_INTERVAL_MS         HVNC_CAPTURE_INTERVAL_MS
#define ADAPTIVE_QUALITY_INTERVAL   0  // Disabled in compile-time config
