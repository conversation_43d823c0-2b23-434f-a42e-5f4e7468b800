@echo off
title HVNC Build System - Compile-Time Performance Profiles
color 0A

echo ===============================================
echo           HVNC Build System v3.0
echo   Compile-Time Performance Configuration
echo ===============================================
echo.
echo Available Performance Profiles:
echo.
echo [1] High Performance - Maximum speed, lower quality
echo     * JPEG Quality: 35
echo     * Frame Rate: 60 FPS
echo     * Optimized for gaming and real-time applications
echo.
echo [2] Balanced - Good speed and quality balance
echo     * JPEG Quality: 65
echo     * Frame Rate: 45 FPS
echo     * Optimized for general office use
echo.
echo [3] High Quality - Best visual quality, slower speed
echo     * JPEG Quality: 90
echo     * Frame Rate: 30 FPS
echo     * Optimized for design and detailed work
echo.
set /p PROFILE_CHOICE="Select performance profile (1-3): "
echo [DEBUG] User input: '%PROFILE_CHOICE%'

REM Validate input
if "%PROFILE_CHOICE%"=="1" (
    set "PROFILE_NAME=High Performance"
    set "PROFILE_DEFINE=HVNC_HIGH_PERFORMANCE"
    set "OUTPUT_SUFFIX=_HighPerf"
) else if "%PROFILE_CHOICE%"=="2" (
    set "PROFILE_NAME=Balanced"
    set "PROFILE_DEFINE=HVNC_BALANCED"
    set "OUTPUT_SUFFIX=_Balanced"
) else if "%PROFILE_CHOICE%"=="3" (
    set "PROFILE_NAME=High Quality"
    set "PROFILE_DEFINE=HVNC_HIGH_QUALITY"
    set "OUTPUT_SUFFIX=_HighQuality"
) else (
    echo [ERROR] Invalid selection. Please choose 1, 2, or 3.
    pause
    exit /b 1
)

echo.
echo [INFO] Selected Profile: %PROFILE_NAME%
echo [INFO] Preprocessor Define: %PROFILE_DEFINE%
echo [DEBUG] Output Suffix: %OUTPUT_SUFFIX%
echo.

REM Try to find MSBuild in common locations
set "MSBUILD_PATH="

REM VS2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"

REM VS2022 BuildTools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe"

REM VS2019 Community
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"

REM VS2019 BuildTools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"

if "%MSBUILD_PATH%"=="" (
    echo [ERROR] MSBuild.exe not found!
    echo Please install Visual Studio 2019/2022 or Build Tools
    echo.
    pause
    exit /b 1
)

echo [INFO] MSBuild found: %MSBUILD_PATH%
echo.

REM Clean previous builds
echo [STEP 1/4] Cleaning previous builds...
echo [INFO] Attempting to terminate any running server processes...
taskkill /F /IM "Server.exe" >nul 2>&1
taskkill /F /IM "Server_HighPerf.exe" >nul 2>&1
taskkill /F /IM "Server_Balanced.exe" >nul 2>&1
taskkill /F /IM "Server_HighQuality.exe" >nul 2>&1
echo [INFO] Attempting to terminate any running client processes...
taskkill /F /IM "Client.exe" >nul 2>&1
taskkill /F /IM "Client_HighPerf.exe" >nul 2>&1
taskkill /F /IM "Client_Balanced.exe" >nul 2>&1
taskkill /F /IM "Client_HighQuality.exe" >nul 2>&1
echo [INFO] Processes terminated.
if exist "Client\_bin\Release\Win32\Client%OUTPUT_SUFFIX%.exe" del "Client\_bin\Release\Win32\Client%OUTPUT_SUFFIX%.exe" >nul 2>&1
if exist "Server\_bin\Release\Win32\Server%OUTPUT_SUFFIX%.exe" del "Server\_bin\Release\Win32\Server%OUTPUT_SUFFIX%.exe" >nul 2>&1
if exist "Client\_bin\Release\Win32\Client.exe" del "Client\_bin\Release\Win32\Client.exe" >nul 2>&1
if exist "Server\_bin\Release\Win32\Server.exe" del "Server\_bin\Release\Win32\Server.exe" >nul 2>&1
echo [SUCCESS] Previous builds cleaned
echo.

REM Configure profile by copying the appropriate configuration file
echo [INFO] Configuring profile: %PROFILE_NAME%
echo [DEBUG] Profile define: %PROFILE_DEFINE%

if "%PROFILE_DEFINE%"=="HVNC_HIGH_PERFORMANCE" (
    echo [DEBUG] Copying High Performance config...
    copy "common\CompileTimeConfig_HighPerf.h" "common\CompileTimeConfig.h" >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Failed to copy High Performance config
        exit /b 1
    )
    echo [SUCCESS] High Performance configuration applied
)

if "%PROFILE_DEFINE%"=="HVNC_BALANCED" (
    echo [DEBUG] Copying Balanced config...
    copy "common\CompileTimeConfig_Balanced.h" "common\CompileTimeConfig.h" >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Failed to copy Balanced config
        exit /b 1
    )
    echo [SUCCESS] Balanced configuration applied
)

if "%PROFILE_DEFINE%"=="HVNC_HIGH_QUALITY" (
    echo [DEBUG] Copying High Quality config...
    copy "common\CompileTimeConfig_HighQuality.h" "common\CompileTimeConfig.h" >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Failed to copy High Quality config
        exit /b 1
    )
    echo [SUCCESS] High Quality configuration applied
)

REM Verify the configuration was applied correctly
echo [DEBUG] Verifying configuration file...
echo [DEBUG] Current JPEG Quality setting:
findstr /C:"HVNC_JPEG_QUALITY" "common\CompileTimeConfig.h"
echo [DEBUG] Current Profile Name:
findstr /C:"HVNC_PROFILE_NAME" "common\CompileTimeConfig.h"

REM Build Client

REM Build Client
echo [STEP 2/4] Building HVNC Client - %PROFILE_NAME% Profile...
"%MSBUILD_PATH%" Client\HVNC.vcxproj /p:Configuration=Release /p:Platform=Win32 /p:TargetName="Client%OUTPUT_SUFFIX%" /verbosity:minimal
if errorlevel 1 (
    echo [ERROR] Client build failed!
    echo Check the output above for errors
    echo.
    pause
    exit /b 1
)
echo [SUCCESS] Client built successfully
echo.

REM Build Server
echo [STEP 3/4] Building HVNC Server - %PROFILE_NAME% Profile...
"%MSBUILD_PATH%" Server\Server.vcxproj /p:Configuration=Release /p:Platform=Win32 /p:TargetName="Server%OUTPUT_SUFFIX%" /verbosity:minimal
if errorlevel 1 (
    echo [ERROR] Server build failed!
    echo Check the output above for errors
    echo.
    pause
    exit /b 1
)
echo [SUCCESS] Server built successfully
echo.

REM Configuration file remains as profile-specific for this build
echo [INFO] Build completed with %PROFILE_NAME% configuration
echo [INFO] CompileTimeConfig.h contains profile-specific settings

REM Verify builds
echo [STEP 4/4] Verifying builds...
if not exist "Client\_bin\Release\Win32\Client%OUTPUT_SUFFIX%.exe" (
    echo [ERROR] Client%OUTPUT_SUFFIX%.exe not found after build!
    pause
    exit /b 1
)
if not exist "Server\_bin\Release\Win32\Server%OUTPUT_SUFFIX%.exe" (
    echo [ERROR] Server%OUTPUT_SUFFIX%.exe not found after build!
    pause
    exit /b 1
)

echo [SUCCESS] All builds verified
echo.
echo ===============================================
echo              BUILD COMPLETED!
echo ===============================================
echo.
echo Performance Profile: %PROFILE_NAME%
echo Client executable: Client\_bin\Release\Win32\Client%OUTPUT_SUFFIX%.exe
echo Server executable: Server\_bin\Release\Win32\Server%OUTPUT_SUFFIX%.exe
echo.
echo Compile-time features:
echo  - TurboJPEG compression (Quality: hardcoded)
echo  - Differential capture (Thresholds: hardcoded)
echo  - Network protocol with 4-byte framing
echo  - No runtime performance controls (optimized)
echo  - Profile: %PROFILE_NAME%
echo.
echo Ready for deployment!
echo.
pause
