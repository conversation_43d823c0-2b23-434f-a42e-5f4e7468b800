@echo off
title HVNC Build System
color 0A

echo ===============================================
echo           HVNC Build System v2.0
echo    High-Performance TurboJPEG Edition
echo ===============================================
echo.

REM Try to find MSBuild in common locations
set "MSBUILD_PATH="

REM VS2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"

REM VS2022 BuildTools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe"

REM VS2019 Community
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"

REM VS2019 BuildTools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"

if "%MSBUILD_PATH%"=="" (
    echo [ERROR] MSBuild.exe not found!
    echo Please install Visual Studio 2019/2022 or Build Tools
    echo.
    pause
    exit /b 1
)

echo [INFO] MSBuild found: %MSBUILD_PATH%
echo.

REM Clean previous builds
echo [STEP 1/4] Cleaning previous builds...
if exist "Client\_bin\Release\Win32\Client.exe" del "Client\_bin\Release\Win32\Client.exe" >nul 2>&1
if exist "Server\_bin\Release\Win32\Server.exe" del "Server\_bin\Release\Win32\Server.exe" >nul 2>&1
echo [SUCCESS] Previous builds cleaned
echo.

REM Build Client
echo [STEP 2/4] Building HVNC Client with TurboJPEG...
"%MSBUILD_PATH%" Client\HVNC.vcxproj /p:Configuration=Release /p:Platform=Win32 /verbosity:minimal
if errorlevel 1 (
    echo [ERROR] Client build failed!
    echo Check the output above for errors
    echo.
    pause
    exit /b 1
)
echo [SUCCESS] Client built successfully
echo.

REM Build Server
echo [STEP 3/4] Building HVNC Server with TurboJPEG...
"%MSBUILD_PATH%" Server\Server.vcxproj /p:Configuration=Release /p:Platform=Win32 /verbosity:minimal
if errorlevel 1 (
    echo [ERROR] Server build failed!
    echo Check the output above for errors
    echo.
    pause
    exit /b 1
)
echo [SUCCESS] Server built successfully
echo.

REM Verify builds
echo [STEP 4/4] Verifying builds...
if not exist "Client\_bin\Release\Win32\Client.exe" (
    echo [ERROR] Client.exe not found after build!
    pause
    exit /b 1
)
if not exist "Server\_bin\Release\Win32\Server.exe" (
    echo [ERROR] Server.exe not found after build!
    pause
    exit /b 1
)

echo [SUCCESS] All builds verified
echo.
echo ===============================================
echo              BUILD COMPLETED!
echo ===============================================
echo.
echo Client executable: Client\_bin\Release\Win32\Client.exe
echo Server executable: Server\_bin\Release\Win32\Server.exe
echo.
echo Features included:
echo  - TurboJPEG high-performance compression
echo  - Differential capture system
echo  - Network protocol with 4-byte framing
echo  - Enhanced error handling and logging
echo.
echo Ready for deployment!
echo.
pause
