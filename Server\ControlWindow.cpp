#include "ControlWindow.h"
#include <commctrl.h>
#include <stdio.h>
#include "../common/CompileTimeConfig.h"

#pragma comment(lib, "comctl32.lib")

static const TCHAR *className = TEXT("HiddenDesktop_ControlWindow");
static const TCHAR *titlePattern = TEXT("Desktop@%S | HVNC - Stable [v2.0]");

// COMPILE-TIME PERFORMANCE SETTINGS - NO RUNTIME ADJUSTMENT
// All performance settings are now hardcoded at compile time
// Runtime performance controls have been removed for optimization

/*
 * REMOVED: Runtime performance settings structure
 * All settings are now compile-time constants defined in CompileTimeConfig.h
 *
 * Previous runtime settings replaced with:
 * - HVNC_JPEG_QUALITY (compile-time constant)
 * - HVNC_FRAME_RATE_LIMIT (compile-time constant)
 * - HVNC_COMPRESSION_LEVEL (compile-time constant)
 * - HVNC_USE_HARDWARE_ACCEL (compile-time constant)
 * - HVNC_ADAPTIVE_QUALITY (compile-time constant)
 */

// Basic performance monitoring
static DWORD g_lastFrameTime = 0;
static DWORD g_frameCount = 0;
static DWORD g_avgFrameTime = 16;

// Simple initialization - no advanced Windows detection
static void InitializeBasic()
{
    // Just set basic defaults - no version detection to avoid crashes
    g_lastFrameTime = GetTickCount();
}

BOOL CW_Register(WNDPROC lpfnWndProc)
{
   InitializeBasic();

   WNDCLASSEX wndClass;
   wndClass.cbSize        = sizeof(WNDCLASSEX);
   wndClass.style         = CS_DBLCLKS;
   wndClass.lpfnWndProc   = lpfnWndProc;
   wndClass.cbClsExtra    = 0;
   wndClass.cbWndExtra    = 0;
   wndClass.hInstance     = GetModuleHandle(NULL);
   wndClass.hIcon         = LoadIcon(NULL, IDI_APPLICATION);
   wndClass.hCursor       = LoadCursor(NULL, IDC_ARROW);
   wndClass.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
   wndClass.lpszMenuName  = NULL;
   wndClass.lpszClassName = className;
   wndClass.hIconSm       = LoadIcon(NULL, IDI_APPLICATION);
   return RegisterClassEx(&wndClass);
}

HWND CW_Create(DWORD uhid, DWORD width, DWORD height)
{
   TCHAR title[100];
   IN_ADDR addr;
   addr.S_un.S_addr = uhid;

   wsprintf(title, titlePattern, inet_ntoa(addr));

   HWND hWnd = CreateWindow(className,
      title,
      WS_MAXIMIZEBOX | WS_MINIMIZEBOX | WS_SIZEBOX | WS_SYSMENU,
      CW_USEDEFAULT,
      CW_USEDEFAULT,
      width,
      height,
      NULL,
      NULL,
      GetModuleHandle(NULL),
      NULL);

   if(hWnd == NULL)
      return NULL;

   ShowWindow(hWnd, SW_SHOW);
   return hWnd;
}

// Simple cleanup function
void CW_Cleanup()
{
   // Nothing to cleanup in simple version
}

// Enable safe mode (already enabled by default in this version)
void CW_EnableSafeMode()
{
   // Already in safe mode
}

// Check if safe mode is enabled (always true in this version)
BOOL CW_IsSafeModeEnabled()
{
   return TRUE;
}

/*
 * REMOVED: Runtime performance control functions
 *
 * The following functions have been removed as all performance settings
 * are now compile-time constants:
 *
 * - CW_SetImageQuality() -> Use HVNC_JPEG_QUALITY constant
 * - CW_SetFrameRate() -> Use HVNC_FRAME_RATE_LIMIT constant
 * - Performance adjustment dialogs and UI controls
 *
 * This results in smaller, more optimized executables with no runtime overhead.
 */

// Legacy compatibility - these functions now do nothing
void CW_SetImageQuality(ImageQuality quality)
{
    // REMOVED: Runtime quality adjustment
    // Quality is now hardcoded at compile time: HVNC_JPEG_QUALITY
}

void CW_SetFrameRate(DWORD fps)
{
    // REMOVED: Runtime frame rate adjustment
    // Frame rate is now hardcoded at compile time: HVNC_FRAME_RATE_LIMIT
}

void CW_SetPerformanceSettings(const PerformanceSettings* settings)
{
   if (settings) {
       g_perfSettings = *settings;
       CW_SetFrameRate(settings->frameRateLimit);
   }
}

void CW_GetPerformanceSettings(PerformanceSettings* settings)
{
   if (settings) {
       *settings = g_perfSettings;
   }
}

// Simple frame rate monitoring
BOOL CW_ShouldSkipFrame()
{
   if (g_perfSettings.frameRateLimit == 0) {
       return FALSE;
   }

   DWORD currentTime = GetTickCount();
   DWORD timeSinceLastFrame = currentTime - g_lastFrameTime;

   if (timeSinceLastFrame < g_avgFrameTime) {
       return TRUE;
   }

   g_lastFrameTime = currentTime;
   return FALSE;
}

/*
 * REMOVED: Performance configuration dialog IDs and controls
 *
 * All runtime performance adjustment UI has been removed:
 * - Performance dialog (IDD_PERFORMANCE_DIALOG)
 * - Quality slider (IDC_QUALITY_SLIDER)
 * - Frame rate slider (IDC_FRAMERATE_SLIDER)
 * - Compression slider (IDC_COMPRESSION_SLIDER)
 * - Hardware acceleration checkbox (IDC_HARDWARE_ACCEL)
 * - Adaptive quality checkbox (IDC_ADAPTIVE_QUALITY)
 * - Preset combo box (IDC_PRESET_COMBO)
 * - Apply button (IDC_APPLY_BUTTON)
 *
 * Performance is now configured at compile time only.
 */

// Update labels in the performance dialog
static void UpdatePerformanceLabels(HWND hDlg)
{
    TCHAR buffer[64];

    // Update quality label
    wsprintf(buffer, TEXT("Image Quality: %d%%"), g_perfSettings.imageQuality);
    SetDlgItemText(hDlg, IDC_QUALITY_LABEL, buffer);

    // Update frame rate label
    wsprintf(buffer, TEXT("Frame Rate: %d FPS"), g_perfSettings.frameRateLimit);
    SetDlgItemText(hDlg, IDC_FRAMERATE_LABEL, buffer);

    // Update compression label
    wsprintf(buffer, TEXT("Compression: Level %d"), g_perfSettings.compressionLevel);
    SetDlgItemText(hDlg, IDC_COMPRESSION_LABEL, buffer);
}

/*
 * REMOVED: Performance preset and dialog functions
 *
 * All runtime performance adjustment UI has been disabled.
 * Performance is now configured at compile time only.
 */

/*
// Apply preset to controls
static void ApplyPreset(HWND hDlg, int preset)
{
    // DISABLED: Runtime performance presets
    // Use compile-time profiles instead: High Performance, Balanced, High Quality
}
*/

/*
 * REMOVED: Performance dialog procedure
 *
 * All performance adjustment dialogs have been disabled.
 * Performance settings are now compile-time constants.
 */

/*
// Performance dialog procedure
static INT_PTR CALLBACK PerformanceDialogProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
{
    // DISABLED: Performance dialog UI
    return FALSE;
}
*/

/*
 * REMOVED: Performance dialog message handlers
 * All performance UI controls have been disabled.
 */

/*
 * REMOVED: Performance configuration window class and controls
 * All performance UI has been disabled for compile-time optimization.
 */

/*
 * REMOVED: Performance window procedure
 * All performance UI controls have been disabled.
 */

/*
// Performance window procedure
static LRESULT CALLBACK PerformanceWindowProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message) {
        case WM_CREATE:
        {
            // Create controls
            CreateWindow(TEXT("STATIC"), TEXT("Performance Settings"),
                WS_VISIBLE | WS_CHILD | SS_CENTER,
                10, 10, 280, 20, hWnd, NULL, GetModuleHandle(NULL), NULL);

            // Preset combo box
            CreateWindow(TEXT("STATIC"), TEXT("Preset:"),
                WS_VISIBLE | WS_CHILD,
                10, 40, 60, 20, hWnd, NULL, GetModuleHandle(NULL), NULL);

            g_hPresetCombo = CreateWindow(TEXT("COMBOBOX"), NULL,
                WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
                80, 38, 120, 100, hWnd, (HMENU)IDC_PRESET_COMBO, GetModuleHandle(NULL), NULL);

            SendMessage(g_hPresetCombo, CB_ADDSTRING, 0, (LPARAM)TEXT("Gaming (Fast)"));
            SendMessage(g_hPresetCombo, CB_ADDSTRING, 0, (LPARAM)TEXT("Office (Balanced)"));
            SendMessage(g_hPresetCombo, CB_ADDSTRING, 0, (LPARAM)TEXT("Design (Quality)"));
            SendMessage(g_hPresetCombo, CB_SETCURSEL, 1, 0);

            // Quality slider
            g_hQualityLabel = CreateWindow(TEXT("STATIC"), TEXT("Image Quality: 60%"),
                WS_VISIBLE | WS_CHILD,
                10, 70, 200, 20, hWnd, (HMENU)IDC_QUALITY_LABEL, GetModuleHandle(NULL), NULL);

            g_hQualitySlider = CreateWindow(TRACKBAR_CLASS, NULL,
                WS_VISIBLE | WS_CHILD | TBS_HORZ | TBS_AUTOTICKS,
                10, 90, 280, 30, hWnd, (HMENU)IDC_QUALITY_SLIDER, GetModuleHandle(NULL), NULL);

            SendMessage(g_hQualitySlider, TBM_SETRANGE, TRUE, MAKELONG(20, 100));
            SendMessage(g_hQualitySlider, TBM_SETPOS, TRUE, g_perfSettings.imageQuality);

            // Frame rate slider
            g_hFrameRateLabel = CreateWindow(TEXT("STATIC"), TEXT("Frame Rate: 60 FPS"),
                WS_VISIBLE | WS_CHILD,
                10, 130, 200, 20, hWnd, (HMENU)IDC_FRAMERATE_LABEL, GetModuleHandle(NULL), NULL);

            g_hFrameRateSlider = CreateWindow(TRACKBAR_CLASS, NULL,
                WS_VISIBLE | WS_CHILD | TBS_HORZ | TBS_AUTOTICKS,
                10, 150, 280, 30, hWnd, (HMENU)IDC_FRAMERATE_SLIDER, GetModuleHandle(NULL), NULL);

            SendMessage(g_hFrameRateSlider, TBM_SETRANGE, TRUE, MAKELONG(15, 120));
            SendMessage(g_hFrameRateSlider, TBM_SETPOS, TRUE, g_perfSettings.frameRateLimit);

            // Compression slider
            g_hCompressionLabel = CreateWindow(TEXT("STATIC"), TEXT("Compression: Level 6"),
                WS_VISIBLE | WS_CHILD,
                10, 190, 200, 20, hWnd, (HMENU)IDC_COMPRESSION_LABEL, GetModuleHandle(NULL), NULL);

            g_hCompressionSlider = CreateWindow(TRACKBAR_CLASS, NULL,
                WS_VISIBLE | WS_CHILD | TBS_HORZ | TBS_AUTOTICKS,
                10, 210, 280, 30, hWnd, (HMENU)IDC_COMPRESSION_SLIDER, GetModuleHandle(NULL), NULL);

            SendMessage(g_hCompressionSlider, TBM_SETRANGE, TRUE, MAKELONG(1, 9));
            SendMessage(g_hCompressionSlider, TBM_SETPOS, TRUE, g_perfSettings.compressionLevel);

            // Apply button
            CreateWindow(TEXT("BUTTON"), TEXT("Apply & Save"),
                WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                10, 250, 100, 30, hWnd, (HMENU)IDC_APPLY_BUTTON, GetModuleHandle(NULL), NULL);

            // Close button
            CreateWindow(TEXT("BUTTON"), TEXT("Close"),
                WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                190, 250, 100, 30, hWnd, (HMENU)IDCANCEL, GetModuleHandle(NULL), NULL);

            UpdatePerformanceLabels(hWnd);
            return 0;
        }

        case WM_HSCROLL:
        {
            // Handle real-time slider changes
            HWND hSlider = (HWND)lParam;
            int pos = SendMessage(hSlider, TBM_GETPOS, 0, 0);

            if (hSlider == g_hQualitySlider) {
                g_perfSettings.imageQuality = static_cast<ImageQuality>(pos);
                CW_SetImageQuality(static_cast<ImageQuality>(pos));

                TCHAR buffer[64];
                wsprintf(buffer, TEXT("Image Quality: %d%%"), pos);
                SetWindowText(g_hQualityLabel, buffer);
            }
            else if (hSlider == g_hFrameRateSlider) {
                g_perfSettings.frameRateLimit = pos;
                CW_SetFrameRate(pos);

                TCHAR buffer[64];
                wsprintf(buffer, TEXT("Frame Rate: %d FPS"), pos);
                SetWindowText(g_hFrameRateLabel, buffer);
            }
            else if (hSlider == g_hCompressionSlider) {
                g_perfSettings.compressionLevel = pos;

                TCHAR buffer[64];
                wsprintf(buffer, TEXT("Compression: Level %d"), pos);
                SetWindowText(g_hCompressionLabel, buffer);
            }

            return 0;
        }

        case WM_COMMAND:
        {
            switch (LOWORD(wParam)) {
                case IDC_PRESET_COMBO:
                    if (HIWORD(wParam) == CBN_SELCHANGE) {
                        int sel = SendMessage(g_hPresetCombo, CB_GETCURSEL, 0, 0);

                        // Apply preset
                        switch (sel) {
                            case 0: // Gaming
                                SendMessage(g_hQualitySlider, TBM_SETPOS, TRUE, 40);
                                SendMessage(g_hFrameRateSlider, TBM_SETPOS, TRUE, 45);
                                SendMessage(g_hCompressionSlider, TBM_SETPOS, TRUE, 4);
                                break;
                            case 1: // Office
                                SendMessage(g_hQualitySlider, TBM_SETPOS, TRUE, 60);
                                SendMessage(g_hFrameRateSlider, TBM_SETPOS, TRUE, 60);
                                SendMessage(g_hCompressionSlider, TBM_SETPOS, TRUE, 6);
                                break;
                            case 2: // Design
                                SendMessage(g_hQualitySlider, TBM_SETPOS, TRUE, 85);
                                SendMessage(g_hFrameRateSlider, TBM_SETPOS, TRUE, 30);
                                SendMessage(g_hCompressionSlider, TBM_SETPOS, TRUE, 8);
                                break;
                        }

                        // Apply changes immediately
                        g_perfSettings.imageQuality = static_cast<ImageQuality>(SendMessage(g_hQualitySlider, TBM_GETPOS, 0, 0));
                        g_perfSettings.frameRateLimit = SendMessage(g_hFrameRateSlider, TBM_GETPOS, 0, 0);
                        g_perfSettings.compressionLevel = SendMessage(g_hCompressionSlider, TBM_GETPOS, 0, 0);

                        CW_SetImageQuality(g_perfSettings.imageQuality);
                        CW_SetFrameRate(g_perfSettings.frameRateLimit);

                        // Update labels
                        TCHAR buffer[64];
                        wsprintf(buffer, TEXT("Image Quality: %d%%"), g_perfSettings.imageQuality);
                        SetWindowText(g_hQualityLabel, buffer);
                        wsprintf(buffer, TEXT("Frame Rate: %d FPS"), g_perfSettings.frameRateLimit);
                        SetWindowText(g_hFrameRateLabel, buffer);
                        wsprintf(buffer, TEXT("Compression: Level %d"), g_perfSettings.compressionLevel);
                        SetWindowText(g_hCompressionLabel, buffer);
                    }
                    break;

                case IDC_APPLY_BUTTON:
                {
                    // Save current settings to file
                    FILE* file = fopen("hvnc_performance.ini", "w");
                    if (file) {
                        fprintf(file, "# HVNC Performance Configuration - Real-time Updated\n");
                        fprintf(file, "jpeg_quality=%d\n", g_perfSettings.imageQuality);
                        fprintf(file, "frame_rate=%d\n", g_perfSettings.frameRateLimit);
                        fprintf(file, "compression_level=%d\n", g_perfSettings.compressionLevel);
                        fprintf(file, "hardware_accel=true\n");
                        fprintf(file, "adaptive_quality=false\n");
                        fprintf(file, "direct_capture=false\n");
                        fprintf(file, "windows11_optimizations=false\n");
                        fprintf(file, "use_compression=true\n");
                        fprintf(file, "profile_name=Real-time\n");
                        fclose(file);

                        MessageBox(hWnd, TEXT("Settings saved to hvnc_performance.ini"), TEXT("Settings Saved"), MB_OK | MB_ICONINFORMATION);
                    }
                    break;
                }

                case IDCANCEL:
                    DestroyWindow(hWnd);
                    break;
            }
            break;
        }

        case WM_CLOSE:
            DestroyWindow(hWnd);
            return 0;

        case WM_DESTROY:
            g_hPerformanceDialog = NULL;
            return 0;
    }

    return DefWindowProc(hWnd, message, wParam, lParam);
}
*/

/*
 * REMOVED: Performance configuration dialog functions
 * All performance adjustment UI has been disabled.
 */

/*
// Show performance configuration window
void CW_ShowPerformanceDialog(HWND hParent)
{
    if (g_hPerformanceDialog != NULL) {
        // Window already open, bring to front
        SetForegroundWindow(g_hPerformanceDialog);
        return;
    }

    // Register window class if not already registered
    if (!g_perfClassRegistered) {
        WNDCLASSEX wc = { 0 };
        wc.cbSize = sizeof(WNDCLASSEX);
        wc.style = CS_HREDRAW | CS_VREDRAW;
        wc.lpfnWndProc = PerformanceWindowProc;
        wc.hInstance = GetModuleHandle(NULL);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        wc.hbrBackground = (HBRUSH)(COLOR_BTNFACE + 1);
        wc.lpszClassName = perfWindowClass;
        wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
        wc.hIconSm = LoadIcon(NULL, IDI_APPLICATION);

        if (RegisterClassEx(&wc)) {
            g_perfClassRegistered = TRUE;
        }
    }

    // Create the performance window
    g_hPerformanceDialog = CreateWindow(
        perfWindowClass,
        TEXT("HVNC Performance Settings"),
        WS_OVERLAPPED | WS_CAPTION | WS_SYSMENU | WS_MINIMIZEBOX,
        CW_USEDEFAULT, CW_USEDEFAULT,
        320, 320,
        hParent,
        NULL,
        GetModuleHandle(NULL),
        NULL
    );

    // DISABLED: Performance dialog creation
}
*/

// Stub function for compatibility
void CW_ShowPerformanceDialog(HWND hParent)
{
    // REMOVED: Performance dialog functionality
    // Performance is now configured at compile time only
    MessageBox(hParent,
        TEXT("Performance settings are now configured at compile time.\n\n")
        TEXT("Use build.bat to select from predefined profiles:\n")
        TEXT("- High Performance (fast, lower quality)\n")
        TEXT("- Balanced (moderate speed and quality)\n")
        TEXT("- High Quality (slower, best quality)\n\n")
        TEXT("Current profile: ") TEXT(HVNC_PROFILE_NAME),
        TEXT("Performance Configuration"),
        MB_OK | MB_ICONINFORMATION);
}