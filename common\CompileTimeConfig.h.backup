#pragma once

// High Quality Profile Configuration
// Optimized for design work and detailed visual tasks
// - Maximum quality, slower speed
// - Lower frame rate for better visual accuracy

#define HVNC_HIGH_QUALITY

// JPEG compression quality (1-100, maximum quality)
#define HVNC_JPEG_QUALITY 90

// Frame rate limit (frames per second)
#define HVNC_FRAME_RATE_LIMIT 30

// Capture interval in milliseconds (calculated from frame rate)
#define HVNC_CAPTURE_INTERVAL_MS (1000 / HVNC_FRAME_RATE_LIMIT)

// Differential capture threshold (0-255, high sensitivity for quality)
#define HVNC_PIXEL_DIFF_THRESHOLD 25

// LZNT1 compression level (1-12, maximum compression)
#define HVNC_COMPRESSION_LEVEL 8

// Profile information for logging
#define HVNC_PROFILE_NAME "High Quality"
#define HVNC_PROFILE_DESCRIPTION "Design/Quality optimized"

// Logging macro for profile information
#define HVNC_LOG_PROFILE_INFO() \
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, \
        "HVNC Profile: %s | JPEG Quality: %d | Frame Rate: %d FPS | Compression: %d", \
        HVNC_PROFILE_NAME, HVNC_JPEG_QUALITY, HVNC_FRAME_RATE_LIMIT, HVNC_COMPRESSION_LEVEL)
