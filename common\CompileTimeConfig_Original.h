#pragma once

// Compile-Time Performance Configuration
// This file is replaced during build with profile-specific settings
// Original file contains conditional compilation logic

#if defined(HVNC_HIGH_PERFORMANCE)
    // High Performance Profile Configuration
    // Optimized for gaming and real-time applications
    // - Maximum speed, lower quality
    // - High frame rate for responsiveness

    // JPEG compression quality (1-100, lower = faster compression, lower quality)
    #define HVNC_JPEG_QUALITY 35

    // Frame rate limit (frames per second)
    #define HVNC_FRAME_RATE_LIMIT 60

    // Capture interval in milliseconds (calculated from frame rate)
    #define HVNC_CAPTURE_INTERVAL_MS (1000 / HVNC_FRAME_RATE_LIMIT)

    // Differential capture threshold (0-255, lower = more sensitive to changes)
    #define HVNC_PIXEL_DIFF_THRESHOLD 15

    // LZNT1 compression level (1-12, higher = better compression, slower)
    #define HVNC_COMPRESSION_LEVEL 4

    // Profile information for logging
    #define HVNC_PROFILE_NAME "High Performance"
    #define HVNC_PROFILE_DESCRIPTION "Gaming/Real-time optimized"

#elif defined(HVNC_HIGH_QUALITY)
    // High Quality Profile Configuration
    // Optimized for design work and detailed visual tasks
    // - Maximum quality, slower speed
    // - Lower frame rate for better visual accuracy

    // JPEG compression quality (1-100, maximum quality)
    #define HVNC_JPEG_QUALITY 90

    // Frame rate limit (frames per second)
    #define HVNC_FRAME_RATE_LIMIT 30

    // Capture interval in milliseconds (calculated from frame rate)
    #define HVNC_CAPTURE_INTERVAL_MS (1000 / HVNC_FRAME_RATE_LIMIT)

    // Differential capture threshold (0-255, high sensitivity for quality)
    #define HVNC_PIXEL_DIFF_THRESHOLD 25

    // LZNT1 compression level (1-12, maximum compression)
    #define HVNC_COMPRESSION_LEVEL 8

    // Profile information for logging
    #define HVNC_PROFILE_NAME "High Quality"
    #define HVNC_PROFILE_DESCRIPTION "Design/Quality optimized"

#else
    // Balanced Profile Configuration (Default)
    // Optimized for general office use and productivity
    // - Moderate speed and quality balance
    // - Good performance for most applications

    #define HVNC_BALANCED

    // JPEG compression quality (1-100, moderate balance)
    #define HVNC_JPEG_QUALITY 65

    // Frame rate limit (frames per second)
    #define HVNC_FRAME_RATE_LIMIT 45

    // Capture interval in milliseconds (calculated from frame rate)
    #define HVNC_CAPTURE_INTERVAL_MS (1000 / HVNC_FRAME_RATE_LIMIT)

    // Differential capture threshold (0-255, moderate sensitivity)
    #define HVNC_PIXEL_DIFF_THRESHOLD 20

    // LZNT1 compression level (1-12, balanced compression)
    #define HVNC_COMPRESSION_LEVEL 6

    // Profile information for logging
    #define HVNC_PROFILE_NAME "Balanced (Default)"
    #define HVNC_PROFILE_DESCRIPTION "General use optimized"

#endif

// Logging macro for profile information
#define HVNC_LOG_PROFILE_INFO() \
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, \
        "HVNC Profile: %s | JPEG Quality: %d | Frame Rate: %d FPS | Compression: %d", \
        HVNC_PROFILE_NAME, HVNC_JPEG_QUALITY, HVNC_FRAME_RATE_LIMIT, HVNC_COMPRESSION_LEVEL)
