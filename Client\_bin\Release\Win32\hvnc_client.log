[2025-07-21 10:52:25.279] [INFO] HVNC Client logging initialized
[2025-07-21 10:52:25.281] [INFO] HVNC Client starting - Host: ***************, Port: 4043
[2025-07-21 10:52:25.294] [INFO] Hidden desktop thread started successfully
[2025-07-21 10:52:25.297] [INFO] TurboJPEG wrapper initialized successfully
[2025-07-21 10:52:25.299] [INFO] TurboJPEG image processing initialized successfully
[2025-07-21 10:52:25.299] [INFO] Capture system initialized with TurboJPEG compression
[2025-07-21 10:52:25.301] [INFO] HVNC Profile: High Performance | JPEG Quality: 35 | Frame Rate: 60 FPS | Compression: 4
[2025-07-21 10:52:25.301] [INFO] DEBUG: Compile-time constants - Quality: 35, FPS: 60, Interval: 16
[2025-07-21 10:52:25.313] [INFO] Successfully connected to ***************:4043
[2025-07-21 10:52:25.355] [INFO] Successfully connected to ***************:4043
[2025-07-21 10:52:25.374] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:25.380] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:25.383] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:25.385] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:25.429] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:25.429] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:25.429] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:25.429] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:25.493] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:25.501] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 15ms
[2025-07-21 10:52:25.501] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:25.501] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:25.557] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:25.557] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:25.557] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:25.557] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:25.621] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:25.621] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:25.631] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:25.631] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:25.684] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:25.684] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:25.684] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:25.684] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:25.732] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:25.732] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:25.732] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:25.742] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:25.780] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:25.789] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:25.791] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:25.791] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:25.844] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:25.844] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:25.852] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:25.854] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:25.909] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:25.917] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:25.919] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:25.922] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:25.971] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:25.981] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:25.983] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:25.985] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.034] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.034] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.034] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.034] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.098] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.103] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.103] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.103] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.161] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.168] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.168] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.168] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.211] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.211] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.211] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.211] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.257] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.262] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.262] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.262] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.321] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.328] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 15ms
[2025-07-21 10:52:26.328] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.328] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.385] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.391] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.391] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.391] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.450] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.452] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.456] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.458] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.514] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.520] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.522] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.522] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.576] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.576] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.576] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.576] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.639] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.647] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.647] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.651] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.704] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.704] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.704] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.704] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.768] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.776] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.778] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.780] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.829] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.833] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.835] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.837] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.877] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.883] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.883] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.885] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.926] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.931] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.931] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.931] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:26.990] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:26.997] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:26.997] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:26.997] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:27.053] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:27.061] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 16ms
[2025-07-21 10:52:27.061] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:27.061] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:27.117] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:27.117] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:27.117] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:27.117] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:27.180] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:52:27.180] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 10:52:27.180] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 10:52:27.180] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 10:52:27.196] [INFO] InputThread: Socket closed
[2025-07-21 10:52:27.196] [INFO] DesktopThread: Socket closed
[2025-07-21 10:52:27.208] [INFO] TurboJPEG wrapper cleaned up
[2025-07-21 10:52:27.210] [INFO] TurboJPEG image processing cleaned up
[2025-07-21 10:52:27.214] [INFO] Hidden desktop thread finished
[2025-07-21 10:52:27.215] [INFO] HVNC Client logging shutdown
