[2025-07-21 16:49:48.059] [INFO] HVNC Client logging initialized
[2025-07-21 16:49:48.059] [INFO] HVNC Client starting - Host: ***************, Port: 4043
[2025-07-21 16:49:48.059] [INFO] Hidden desktop thread started successfully
[2025-07-21 16:49:48.059] [INFO] TurboJPEG wrapper initialized successfully
[2025-07-21 16:49:48.074] [INFO] TurboJPEG image processing initialized successfully
[2025-07-21 16:49:48.074] [INFO] Capture system initialized with TurboJPEG compression
[2025-07-21 16:49:48.074] [INFO] HVNC Profile: High Quality | JPEG Quality: 90 | Frame Rate: 30 FPS | Compression: 8
[2025-07-21 16:49:48.078] [INFO] DEBUG: Compile-time constants - Quality: 90, FPS: 30, Interval: 33
[2025-07-21 16:49:48.092] [INFO] Successfully connected to ***************:4043
[2025-07-21 16:49:48.153] [INFO] Successfully connected to ***************:4043
[2025-07-21 16:49:48.169] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.169] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.169] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.169] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:48.233] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.246] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.248] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.248] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:48.296] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.296] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.296] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.312] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:48.360] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.360] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.360] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.373] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:48.423] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.423] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.423] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.438] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:48.494] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.500] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.502] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.504] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:48.557] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.557] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.557] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.573] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:48.621] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.621] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.621] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.621] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:48.685] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.685] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.685] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.685] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:48.748] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.748] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.748] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.748] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:48.812] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.812] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.812] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.829] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:48.876] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.876] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.876] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.876] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:48.949] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:48.949] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:48.956] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:48.956] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:49.005] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:49.005] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:49.005] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:49.021] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:49.074] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:49.074] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:49.074] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:49.074] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:49.135] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:49.264] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 16ms
[2025-07-21 16:49:49.423] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:49.434] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:49.496] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:49.502] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:49.504] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:49.504] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:49.556] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:49.556] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:49.573] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:49.575] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:49.620] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:49.620] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:49.620] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:49.620] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:49.684] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:49.684] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:49.684] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:49.700] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:49.751] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:49.751] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:49.751] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:49.764] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:49.812] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:49.812] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:49.825] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:49.827] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:49.877] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:49.877] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:49.877] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:49.877] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:49.940] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:49.951] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:49.951] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:49.951] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:50.004] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:50.019] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 16ms
[2025-07-21 16:49:50.019] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:50.019] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:50.068] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:50.068] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:50.068] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:50.068] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:50.131] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:50.131] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:50.131] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:50.131] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:50.195] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:50.195] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:50.195] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:50.211] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:50.267] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:50.267] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:50.267] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:50.267] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:50.331] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:50.331] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:50.331] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:50.347] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:50.388] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 16:49:50.388] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 16:49:50.388] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 16:49:50.388] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 16:49:50.404] [INFO] DesktopThread: Socket closed
[2025-07-21 16:49:50.404] [INFO] InputThread: Socket closed
[2025-07-21 16:49:50.404] [INFO] TurboJPEG wrapper cleaned up
[2025-07-21 16:49:50.404] [INFO] TurboJPEG image processing cleaned up
[2025-07-21 16:49:50.404] [INFO] Hidden desktop thread finished
[2025-07-21 16:49:50.404] [INFO] HVNC Client logging shutdown
