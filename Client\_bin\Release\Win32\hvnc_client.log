[2025-07-21 11:56:50.395] [INFO] HVNC Client logging initialized
[2025-07-21 11:56:50.401] [INFO] HVNC Client starting - Host: ***************, Port: 4043
[2025-07-21 11:56:50.410] [INFO] Hidden desktop thread started successfully
[2025-07-21 11:56:50.411] [INFO] TurboJPEG wrapper initialized successfully
[2025-07-21 11:56:50.411] [INFO] TurboJPEG image processing initialized successfully
[2025-07-21 11:56:50.411] [INFO] Capture system initialized with TurboJPEG compression
[2025-07-21 11:56:50.415] [INFO] HVNC Profile: High Performance | JPEG Quality: 35 | Frame Rate: 60 FPS | Compression: 4
[2025-07-21 11:56:50.417] [INFO] DEBUG: Compile-time constants - Quality: 35, FPS: 60, Interval: 16
[2025-07-21 11:56:50.430] [INFO] Successfully connected to ***************:4043
[2025-07-21 11:56:50.474] [INFO] Successfully connected to ***************:4043
[2025-07-21 11:56:50.488] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:50.494] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:50.494] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:50.494] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:50.556] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:50.562] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:50.562] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:50.562] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:50.619] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:50.627] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 15ms
[2025-07-21 11:56:50.629] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:50.631] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:50.681] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:50.681] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:50.681] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:50.694] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:50.744] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:50.744] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:50.744] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:50.757] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:50.809] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:50.811] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:50.811] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:50.811] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:50.870] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:50.879] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 15ms
[2025-07-21 11:56:50.881] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:50.883] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:50.935] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:50.935] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:50.935] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:50.935] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:50.981] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:50.981] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:50.981] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:50.981] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.044] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.050] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.050] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.050] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.108] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.108] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.108] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.116] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.156] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.158] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.158] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.158] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.218] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.224] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.226] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.228] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.282] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.290] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.295] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.297] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.345] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.353] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.353] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.355] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.410] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.416] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.420] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.420] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.472] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.480] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.482] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.482] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.536] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.544] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.548] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.550] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.601] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.607] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.607] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.609] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.664] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.666] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.666] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.671] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.709] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.715] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.715] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.715] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.773] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.773] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.773] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.773] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.836] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.844] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 16ms
[2025-07-21 11:56:51.846] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.846] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.899] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.905] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 15ms
[2025-07-21 11:56:51.908] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.908] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:51.963] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:51.970] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:51.973] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:51.974] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:52.027] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:52.032] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:52.035] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:52.037] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:52.089] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:52.096] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:52.098] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:52.101] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:52.153] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:52.159] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:52.159] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:52.159] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:52.217] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:52.217] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:52.217] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:52.225] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:52.279] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:52.286] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:52.286] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:52.286] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:52.343] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 11:56:52.343] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7682 bytes (0.6% ratio) in 0ms
[2025-07-21 11:56:52.357] [DEBUG] TurboJPEG compression successful: 784x561 -> 7682 bytes
[2025-07-21 11:56:52.358] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7682 bytes JPEG
[2025-07-21 11:56:52.365] [INFO] DesktopThread: Socket closed
[2025-07-21 11:56:52.367] [INFO] InputThread: Socket closed
[2025-07-21 11:56:52.369] [INFO] TurboJPEG wrapper cleaned up
[2025-07-21 11:56:52.373] [INFO] TurboJPEG image processing cleaned up
[2025-07-21 11:56:52.377] [INFO] Hidden desktop thread finished
[2025-07-21 11:56:52.379] [INFO] HVNC Client logging shutdown
