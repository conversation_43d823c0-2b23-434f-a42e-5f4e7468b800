[2025-07-21 10:55:58.539] [INFO] HVNC Client logging initialized
[2025-07-21 10:55:58.541] [INFO] HVNC Client starting - Host: ***************, Port: 4043
[2025-07-21 10:55:58.552] [INFO] Hidden desktop thread started successfully
[2025-07-21 10:55:58.554] [INFO] TurboJPEG wrapper initialized successfully
[2025-07-21 10:55:58.555] [INFO] TurboJPEG image processing initialized successfully
[2025-07-21 10:55:58.555] [INFO] Capture system initialized with TurboJPEG compression
[2025-07-21 10:55:58.556] [INFO] HVNC Profile: High Performance | JPEG Quality: 35 | Frame Rate: 60 FPS | Compression: 4
[2025-07-21 10:55:58.556] [INFO] DEBUG: Compile-time constants - Quality: 35, FPS: 60, Interval: 16
[2025-07-21 10:55:58.601] [INFO] Successfully connected to ***************:4043
[2025-07-21 10:55:58.754] [INFO] Successfully connected to ***************:4043
[2025-07-21 10:55:58.987] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:55:59.023] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 46314 bytes (3.5% ratio) in 16ms
[2025-07-21 10:55:59.024] [DEBUG] TurboJPEG compression successful: 784x561 -> 46314 bytes
[2025-07-21 10:55:59.025] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 46314 bytes JPEG
[2025-07-21 10:56:00.167] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:00.191] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 48131 bytes (3.6% ratio) in 0ms
[2025-07-21 10:56:00.192] [DEBUG] TurboJPEG compression successful: 784x561 -> 48131 bytes
[2025-07-21 10:56:00.192] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 48131 bytes JPEG
[2025-07-21 10:56:00.487] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:00.493] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 48170 bytes (3.7% ratio) in 15ms
[2025-07-21 10:56:00.494] [DEBUG] TurboJPEG compression successful: 784x561 -> 48170 bytes
[2025-07-21 10:56:00.495] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 48170 bytes JPEG
[2025-07-21 10:56:00.667] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:00.676] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 48211 bytes (3.7% ratio) in 0ms
[2025-07-21 10:56:00.676] [DEBUG] TurboJPEG compression successful: 784x561 -> 48211 bytes
[2025-07-21 10:56:00.677] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 48211 bytes JPEG
[2025-07-21 10:56:00.853] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:00.863] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 48195 bytes (3.7% ratio) in 0ms
[2025-07-21 10:56:00.864] [DEBUG] TurboJPEG compression successful: 784x561 -> 48195 bytes
[2025-07-21 10:56:00.865] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 48195 bytes JPEG
[2025-07-21 10:56:01.050] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:01.058] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 47727 bytes (3.6% ratio) in 0ms
[2025-07-21 10:56:01.060] [DEBUG] TurboJPEG compression successful: 784x561 -> 47727 bytes
[2025-07-21 10:56:01.061] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 47727 bytes JPEG
[2025-07-21 10:56:01.248] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:01.256] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 48200 bytes (3.7% ratio) in 16ms
[2025-07-21 10:56:01.257] [DEBUG] TurboJPEG compression successful: 784x561 -> 48200 bytes
[2025-07-21 10:56:01.258] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 48200 bytes JPEG
[2025-07-21 10:56:01.466] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:01.476] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 48226 bytes (3.7% ratio) in 16ms
[2025-07-21 10:56:01.477] [DEBUG] TurboJPEG compression successful: 784x561 -> 48226 bytes
[2025-07-21 10:56:01.480] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 48226 bytes JPEG
[2025-07-21 10:56:01.667] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:01.675] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 47778 bytes (3.6% ratio) in 0ms
[2025-07-21 10:56:01.676] [DEBUG] TurboJPEG compression successful: 784x561 -> 47778 bytes
[2025-07-21 10:56:01.677] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 47778 bytes JPEG
[2025-07-21 10:56:01.921] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:01.930] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 48228 bytes (3.7% ratio) in 16ms
[2025-07-21 10:56:01.932] [DEBUG] TurboJPEG compression successful: 784x561 -> 48228 bytes
[2025-07-21 10:56:01.933] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 48228 bytes JPEG
[2025-07-21 10:56:02.108] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:02.120] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 48248 bytes (3.7% ratio) in 0ms
[2025-07-21 10:56:02.120] [DEBUG] TurboJPEG compression successful: 784x561 -> 48248 bytes
[2025-07-21 10:56:02.121] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 48248 bytes JPEG
[2025-07-21 10:56:02.284] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:02.292] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 48255 bytes (3.7% ratio) in 0ms
[2025-07-21 10:56:02.293] [DEBUG] TurboJPEG compression successful: 784x561 -> 48255 bytes
[2025-07-21 10:56:02.294] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 48255 bytes JPEG
[2025-07-21 10:56:02.492] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:02.499] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 48221 bytes (3.7% ratio) in 0ms
[2025-07-21 10:56:02.500] [DEBUG] TurboJPEG compression successful: 784x561 -> 48221 bytes
[2025-07-21 10:56:02.501] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 48221 bytes JPEG
[2025-07-21 10:56:02.678] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:02.687] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 48220 bytes (3.7% ratio) in 0ms
[2025-07-21 10:56:02.688] [DEBUG] TurboJPEG compression successful: 784x561 -> 48220 bytes
[2025-07-21 10:56:02.688] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 48220 bytes JPEG
[2025-07-21 10:56:02.840] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:02.849] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 48202 bytes (3.7% ratio) in 16ms
[2025-07-21 10:56:02.850] [DEBUG] TurboJPEG compression successful: 784x561 -> 48202 bytes
[2025-07-21 10:56:02.851] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 48202 bytes JPEG
[2025-07-21 10:56:03.033] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:03.041] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 47672 bytes (3.6% ratio) in 0ms
[2025-07-21 10:56:03.042] [DEBUG] TurboJPEG compression successful: 784x561 -> 47672 bytes
[2025-07-21 10:56:03.043] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 47672 bytes JPEG
[2025-07-21 10:56:03.208] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:03.222] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 107963 bytes (3.0% ratio) in 0ms
[2025-07-21 10:56:03.223] [DEBUG] TurboJPEG compression successful: 1536x793 -> 107963 bytes
[2025-07-21 10:56:03.225] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 107963 bytes JPEG
[2025-07-21 10:56:04.020] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:04.036] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 107937 bytes (3.0% ratio) in 0ms
[2025-07-21 10:56:04.037] [DEBUG] TurboJPEG compression successful: 1536x793 -> 107937 bytes
[2025-07-21 10:56:04.039] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 107937 bytes JPEG
[2025-07-21 10:56:04.560] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:04.577] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 107967 bytes (3.0% ratio) in 16ms
[2025-07-21 10:56:04.579] [DEBUG] TurboJPEG compression successful: 1536x793 -> 107967 bytes
[2025-07-21 10:56:04.582] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 107967 bytes JPEG
[2025-07-21 10:56:04.770] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:04.785] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 107993 bytes (3.0% ratio) in 0ms
[2025-07-21 10:56:04.786] [DEBUG] TurboJPEG compression successful: 1536x793 -> 107993 bytes
[2025-07-21 10:56:04.788] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 107993 bytes JPEG
[2025-07-21 10:56:05.084] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:05.101] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 111654 bytes (3.1% ratio) in 16ms
[2025-07-21 10:56:05.102] [DEBUG] TurboJPEG compression successful: 1536x793 -> 111654 bytes
[2025-07-21 10:56:05.104] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 111654 bytes JPEG
[2025-07-21 10:56:05.476] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:05.491] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 112140 bytes (3.1% ratio) in 15ms
[2025-07-21 10:56:05.492] [DEBUG] TurboJPEG compression successful: 1536x793 -> 112140 bytes
[2025-07-21 10:56:05.497] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 112140 bytes JPEG
[2025-07-21 10:56:05.905] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:05.919] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 112108 bytes (3.1% ratio) in 15ms
[2025-07-21 10:56:05.920] [DEBUG] TurboJPEG compression successful: 1536x793 -> 112108 bytes
[2025-07-21 10:56:05.922] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 112108 bytes JPEG
[2025-07-21 10:56:06.203] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:06.220] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 111957 bytes (3.1% ratio) in 0ms
[2025-07-21 10:56:06.221] [DEBUG] TurboJPEG compression successful: 1536x793 -> 111957 bytes
[2025-07-21 10:56:06.224] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 111957 bytes JPEG
[2025-07-21 10:56:06.518] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:06.535] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 111868 bytes (3.1% ratio) in 0ms
[2025-07-21 10:56:06.536] [DEBUG] TurboJPEG compression successful: 1536x793 -> 111868 bytes
[2025-07-21 10:56:06.538] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 111868 bytes JPEG
[2025-07-21 10:56:06.788] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:06.807] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 111849 bytes (3.1% ratio) in 16ms
[2025-07-21 10:56:06.808] [DEBUG] TurboJPEG compression successful: 1536x793 -> 111849 bytes
[2025-07-21 10:56:06.810] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 111849 bytes JPEG
[2025-07-21 10:56:07.052] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:07.070] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 112149 bytes (3.1% ratio) in 16ms
[2025-07-21 10:56:07.071] [DEBUG] TurboJPEG compression successful: 1536x793 -> 112149 bytes
[2025-07-21 10:56:07.072] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 112149 bytes JPEG
[2025-07-21 10:56:07.338] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:07.353] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 112314 bytes (3.1% ratio) in 16ms
[2025-07-21 10:56:07.354] [DEBUG] TurboJPEG compression successful: 1536x793 -> 112314 bytes
[2025-07-21 10:56:07.356] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 112314 bytes JPEG
[2025-07-21 10:56:07.704] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:07.738] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 109766 bytes (3.0% ratio) in 0ms
[2025-07-21 10:56:07.739] [DEBUG] TurboJPEG compression successful: 1536x793 -> 109766 bytes
[2025-07-21 10:56:07.741] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 109766 bytes JPEG
[2025-07-21 10:56:08.081] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:08.097] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 110983 bytes (3.0% ratio) in 0ms
[2025-07-21 10:56:08.098] [DEBUG] TurboJPEG compression successful: 1536x793 -> 110983 bytes
[2025-07-21 10:56:08.101] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 110983 bytes JPEG
[2025-07-21 10:56:08.405] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:08.423] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 110750 bytes (3.0% ratio) in 0ms
[2025-07-21 10:56:08.424] [DEBUG] TurboJPEG compression successful: 1536x793 -> 110750 bytes
[2025-07-21 10:56:08.425] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 110750 bytes JPEG
[2025-07-21 10:56:10.308] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:10.325] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 109779 bytes (3.0% ratio) in 16ms
[2025-07-21 10:56:10.326] [DEBUG] TurboJPEG compression successful: 1536x793 -> 109779 bytes
[2025-07-21 10:56:10.329] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 109779 bytes JPEG
[2025-07-21 10:56:10.810] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:10.826] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 109787 bytes (3.0% ratio) in 16ms
[2025-07-21 10:56:10.827] [DEBUG] TurboJPEG compression successful: 1536x793 -> 109787 bytes
[2025-07-21 10:56:10.830] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 109787 bytes JPEG
[2025-07-21 10:56:11.308] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:11.341] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 109345 bytes (3.0% ratio) in 15ms
[2025-07-21 10:56:11.342] [DEBUG] TurboJPEG compression successful: 1536x793 -> 109345 bytes
[2025-07-21 10:56:11.348] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 109345 bytes JPEG
[2025-07-21 10:56:11.783] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:11.799] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 109013 bytes (3.0% ratio) in 0ms
[2025-07-21 10:56:11.800] [DEBUG] TurboJPEG compression successful: 1536x793 -> 109013 bytes
[2025-07-21 10:56:11.802] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 109013 bytes JPEG
[2025-07-21 10:56:12.224] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:12.241] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 108909 bytes (3.0% ratio) in 15ms
[2025-07-21 10:56:12.242] [DEBUG] TurboJPEG compression successful: 1536x793 -> 108909 bytes
[2025-07-21 10:56:12.246] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 108909 bytes JPEG
[2025-07-21 10:56:12.650] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:12.666] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 108953 bytes (3.0% ratio) in 15ms
[2025-07-21 10:56:12.667] [DEBUG] TurboJPEG compression successful: 1536x793 -> 108953 bytes
[2025-07-21 10:56:12.668] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 108953 bytes JPEG
[2025-07-21 10:56:13.115] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:13.132] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 109183 bytes (3.0% ratio) in 16ms
[2025-07-21 10:56:13.133] [DEBUG] TurboJPEG compression successful: 1536x793 -> 109183 bytes
[2025-07-21 10:56:13.135] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 109183 bytes JPEG
[2025-07-21 10:56:13.576] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:13.592] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 109274 bytes (3.0% ratio) in 15ms
[2025-07-21 10:56:13.593] [DEBUG] TurboJPEG compression successful: 1536x793 -> 109274 bytes
[2025-07-21 10:56:13.597] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 109274 bytes JPEG
[2025-07-21 10:56:14.059] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:14.078] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 109426 bytes (3.0% ratio) in 16ms
[2025-07-21 10:56:14.079] [DEBUG] TurboJPEG compression successful: 1536x793 -> 109426 bytes
[2025-07-21 10:56:14.083] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 109426 bytes JPEG
[2025-07-21 10:56:14.566] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:14.603] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 109410 bytes (3.0% ratio) in 16ms
[2025-07-21 10:56:14.604] [DEBUG] TurboJPEG compression successful: 1536x793 -> 109410 bytes
[2025-07-21 10:56:14.607] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 109410 bytes JPEG
[2025-07-21 10:56:14.767] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:14.784] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 109465 bytes (3.0% ratio) in 0ms
[2025-07-21 10:56:14.786] [DEBUG] TurboJPEG compression successful: 1536x793 -> 109465 bytes
[2025-07-21 10:56:14.787] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 109465 bytes JPEG
[2025-07-21 10:56:14.938] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:14.958] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 113461 bytes (3.1% ratio) in 0ms
[2025-07-21 10:56:14.960] [DEBUG] TurboJPEG compression successful: 1536x793 -> 113461 bytes
[2025-07-21 10:56:14.965] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 113461 bytes JPEG
[2025-07-21 10:56:15.430] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:15.457] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 113550 bytes (3.1% ratio) in 16ms
[2025-07-21 10:56:15.458] [DEBUG] TurboJPEG compression successful: 1536x793 -> 113550 bytes
[2025-07-21 10:56:15.464] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 113550 bytes JPEG
[2025-07-21 10:56:15.609] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:15.626] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 113530 bytes (3.1% ratio) in 0ms
[2025-07-21 10:56:15.628] [DEBUG] TurboJPEG compression successful: 1536x793 -> 113530 bytes
[2025-07-21 10:56:15.630] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 113530 bytes JPEG
[2025-07-21 10:56:15.774] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:15.794] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 113359 bytes (3.1% ratio) in 15ms
[2025-07-21 10:56:15.796] [DEBUG] TurboJPEG compression successful: 1536x793 -> 113359 bytes
[2025-07-21 10:56:15.798] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 113359 bytes JPEG
[2025-07-21 10:56:15.954] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:15.972] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 113376 bytes (3.1% ratio) in 0ms
[2025-07-21 10:56:15.973] [DEBUG] TurboJPEG compression successful: 1536x793 -> 113376 bytes
[2025-07-21 10:56:15.975] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 113376 bytes JPEG
[2025-07-21 10:56:16.155] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:16.177] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 113532 bytes (3.1% ratio) in 16ms
[2025-07-21 10:56:16.180] [DEBUG] TurboJPEG compression successful: 1536x793 -> 113532 bytes
[2025-07-21 10:56:16.182] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 113532 bytes JPEG
[2025-07-21 10:56:16.334] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:16.352] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 113800 bytes (3.1% ratio) in 16ms
[2025-07-21 10:56:16.353] [DEBUG] TurboJPEG compression successful: 1536x793 -> 113800 bytes
[2025-07-21 10:56:16.354] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 113800 bytes JPEG
[2025-07-21 10:56:16.492] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:16.508] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 113837 bytes (3.1% ratio) in 16ms
[2025-07-21 10:56:16.509] [DEBUG] TurboJPEG compression successful: 1536x793 -> 113837 bytes
[2025-07-21 10:56:16.513] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 113837 bytes JPEG
[2025-07-21 10:56:16.733] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:16.750] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 113821 bytes (3.1% ratio) in 15ms
[2025-07-21 10:56:16.751] [DEBUG] TurboJPEG compression successful: 1536x793 -> 113821 bytes
[2025-07-21 10:56:16.753] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 113821 bytes JPEG
[2025-07-21 10:56:16.914] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:16.929] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 113860 bytes (3.1% ratio) in 16ms
[2025-07-21 10:56:16.930] [DEBUG] TurboJPEG compression successful: 1536x793 -> 113860 bytes
[2025-07-21 10:56:16.933] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 113860 bytes JPEG
[2025-07-21 10:56:17.110] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:17.128] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 113845 bytes (3.1% ratio) in 0ms
[2025-07-21 10:56:17.129] [DEBUG] TurboJPEG compression successful: 1536x793 -> 113845 bytes
[2025-07-21 10:56:17.131] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 113845 bytes JPEG
[2025-07-21 10:56:17.282] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:17.299] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 113785 bytes (3.1% ratio) in 0ms
[2025-07-21 10:56:17.299] [DEBUG] TurboJPEG compression successful: 1536x793 -> 113785 bytes
[2025-07-21 10:56:17.302] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 113785 bytes JPEG
[2025-07-21 10:56:17.509] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:17.526] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 110956 bytes (3.0% ratio) in 16ms
[2025-07-21 10:56:17.527] [DEBUG] TurboJPEG compression successful: 1536x793 -> 110956 bytes
[2025-07-21 10:56:17.530] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 110956 bytes JPEG
[2025-07-21 10:56:17.900] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:17.917] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 111206 bytes (3.0% ratio) in 15ms
[2025-07-21 10:56:17.918] [DEBUG] TurboJPEG compression successful: 1536x793 -> 111206 bytes
[2025-07-21 10:56:17.920] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 111206 bytes JPEG
[2025-07-21 10:56:18.238] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:18.259] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 111371 bytes (3.0% ratio) in 16ms
[2025-07-21 10:56:18.260] [DEBUG] TurboJPEG compression successful: 1536x793 -> 111371 bytes
[2025-07-21 10:56:18.265] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 111371 bytes JPEG
[2025-07-21 10:56:18.532] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:18.549] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 111452 bytes (3.1% ratio) in 0ms
[2025-07-21 10:56:18.550] [DEBUG] TurboJPEG compression successful: 1536x793 -> 111452 bytes
[2025-07-21 10:56:18.552] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 111452 bytes JPEG
[2025-07-21 10:56:18.832] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:18.850] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 111226 bytes (3.0% ratio) in 16ms
[2025-07-21 10:56:18.851] [DEBUG] TurboJPEG compression successful: 1536x793 -> 111226 bytes
[2025-07-21 10:56:18.854] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 111226 bytes JPEG
[2025-07-21 10:56:19.136] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:19.155] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 111332 bytes (3.0% ratio) in 16ms
[2025-07-21 10:56:19.156] [DEBUG] TurboJPEG compression successful: 1536x793 -> 111332 bytes
[2025-07-21 10:56:19.157] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 111332 bytes JPEG
[2025-07-21 10:56:19.383] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:19.401] [DEBUG] TurboJPEG compression: 1536x793, 3654144 bytes -> 108765 bytes (3.0% ratio) in 16ms
[2025-07-21 10:56:19.402] [DEBUG] TurboJPEG compression successful: 1536x793 -> 108765 bytes
[2025-07-21 10:56:19.404] [DEBUG] BitmapToJpgOptimized: Compressed 1536x793 to 108765 bytes JPEG
[2025-07-21 10:56:19.852] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 35 (compile-time: 35)
[2025-07-21 10:56:19.852] [INFO] InputThread: Socket closed
[2025-07-21 10:56:19.873] [INFO] TurboJPEG wrapper cleaned up
[2025-07-21 10:56:19.874] [INFO] TurboJPEG image processing cleaned up
[2025-07-21 10:56:19.875] [INFO] Hidden desktop thread finished
[2025-07-21 10:56:19.876] [INFO] HVNC Client logging shutdown
