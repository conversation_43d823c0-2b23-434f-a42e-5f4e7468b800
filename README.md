# HVNC - High-Performance Remote Desktop System

## Overview
Advanced HVNC (Hidden Virtual Network Computing) system with TurboJPEG compression and differential capture technology.

## Features
- **TurboJPEG Compression**: 2-6x faster than GDI+ with better compression ratios
- **Differential Capture**: Only transmits changed screen regions
- **Network Protocol**: 4-byte length prefixing for reliable data transmission
- **Performance Monitoring**: Real-time compression statistics
- **Error Handling**: Comprehensive logging and fallback mechanisms

## Quick Start

### Building
Run `build.bat` to compile both client and server:
```batch
build.bat
```

### Usage
1. **Start Server**: Run `Server\_bin\Release\Win32\Server.exe`
2. **Connect Client**: Run `Client\_bin\Release\Win32\Client.exe`
3. **Configure**: Enter server IP and port when prompted

## Architecture

### Client Features
- Hidden desktop creation and management
- TurboJPEG screen compression
- Differential region detection
- Network transmission with proper framing
- Performance logging

### Server Features
- Multi-client connection handling
- JPEG and LZNT1 decompression support
- Real-time screen reconstruction
- Control window interface

## Performance
- **Compression Speed**: 2-6x faster than GDI+
- **Bandwidth Usage**: 10-30% reduction with better compression
- **CPU Usage**: Optimized SIMD instructions
- **Memory**: Efficient allocation and cleanup

## Technical Details

### Network Protocol
```
[4 bytes: Data Length] [N bytes: Compressed Data]
```

### Compression Levels
- Low (30): Maximum compression, lower quality
- Medium (60): Balanced compression and quality  
- High (85): High quality, moderate compression
- Maximum (95): Maximum quality, minimal compression

### File Structure
```
Client/          - Client application
Server/          - Server application  
common/          - Shared libraries
tools/           - Utility tools
build.bat        - Build script
```

## Troubleshooting

### Connection Issues
- Check firewall settings
- Verify server is listening on correct port
- Review client logs in `hvnc_client_log.txt`

### Performance Issues
- Adjust compression quality in code
- Monitor CPU and memory usage
- Check network bandwidth

## Development
Built with Visual Studio 2019/2022, requires Windows SDK and MSBuild.

## License
Educational and research purposes only.
