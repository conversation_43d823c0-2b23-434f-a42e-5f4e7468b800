# HVNC - High-Performance Remote Desktop System

## Overview
Advanced HVNC (Hidden Virtual Network Computing) system with TurboJPEG compression, differential capture technology, and compile-time performance optimization.

## Features
- **TurboJPEG Compression**: 2-6x faster than GDI+ with better compression ratios
- **Compile-Time Performance Profiles**: Three optimized build variants with no runtime overhead
- **Differential Capture**: Only transmits changed screen regions
- **Network Protocol**: 4-byte length prefixing for reliable data transmission
- **No Runtime Controls**: All performance settings hardcoded at compile time for maximum optimization
- **Error Handling**: Comprehensive logging and fallback mechanisms

## Performance Profiles

The HVNC system offers three compile-time performance profiles:

### 🚀 High Performance Profile
- **JPEG Quality**: 35 (lower quality, maximum speed)
- **Frame Rate**: 60 FPS
- **Optimized for**: Gaming, real-time applications
- **Executables**: `Client_HighPerf.exe`, `Server_HighPerf.exe`

### ⚖️ Balanced Profile
- **JPEG Quality**: 65 (moderate quality and speed)
- **Frame Rate**: 45 FPS
- **Optimized for**: General office use, productivity
- **Executables**: `Client_Balanced.exe`, `Server_Balanced.exe`

### 🎨 High Quality Profile
- **JPEG Quality**: 90 (maximum quality, slower speed)
- **Frame Rate**: 30 FPS
- **Optimized for**: Design work, detailed visual tasks
- **Executables**: `Client_HighQuality.exe`, `Server_HighQuality.exe`

## Quick Start

### Building
Run `build.bat` and select your desired performance profile:
```batch
build.bat
```
The build system will prompt you to choose from:
1. High Performance
2. Balanced
3. High Quality

### Usage
1. **Start Server**: Run the appropriate server executable (e.g., `Server_Balanced.exe`)
2. **Connect Client**: Run the matching client executable (e.g., `Client_Balanced.exe`)
3. **Configure**: Enter server IP and port when prompted

**Important**: Always use matching client and server executables from the same performance profile.

## Architecture

### Client Features
- Hidden desktop creation and management
- TurboJPEG screen compression with compile-time quality settings
- Differential region detection with hardcoded thresholds
- Network transmission with proper framing
- Performance logging and monitoring

### Server Features
- Multi-client connection handling
- JPEG and LZNT1 decompression support
- Real-time screen reconstruction
- Control window interface (performance controls removed)

## Performance Benefits

### Compile-Time Optimization
- **No Runtime Overhead**: All performance settings hardcoded at compile time
- **Smaller Executables**: Removed runtime configuration code and UI controls
- **Faster Startup**: No performance setting initialization or file reading
- **Predictable Performance**: Consistent behavior with no runtime adjustments

### Compression Performance
- **Speed**: 2-6x faster than GDI+ for JPEG compression
- **Quality**: Optimized compression algorithms with profile-specific settings
- **CPU Usage**: Reduced overhead from eliminated runtime controls
- **Memory**: Efficient allocation with compile-time constants

## Technical Details

### Compile-Time Configuration
All performance settings are defined in `common/CompileTimeConfig.h`:
- `HVNC_JPEG_QUALITY`: JPEG compression quality (35/65/90)
- `HVNC_FRAME_RATE_LIMIT`: Maximum frame rate (60/45/30 FPS)
- `HVNC_CAPTURE_INTERVAL_MS`: Capture timing intervals
- `HVNC_PIXEL_DIFF_THRESHOLD`: Differential capture sensitivity
- `HVNC_COMPRESSION_LEVEL`: LZNT1 compression level

### Network Protocol
```
[4 bytes: Data Length (little-endian)] [N bytes: Compressed Data (JPEG/LZNT1)]
```

### Build System
- **Profile Selection**: Interactive menu during build process
- **Preprocessor Definitions**: Automatic profile constant injection
- **Output Naming**: Profile-specific executable names
- **Validation**: Compile-time constant validation

### File Structure
```
Client/                    - Client application
Server/                    - Server application
common/                    - Shared libraries
├── CompileTimeConfig.h    - Performance profile definitions
├── TurboJPEGWrapper.*     - TurboJPEG integration
└── DifferentialCapture.*  - Screen change detection
build.bat                  - Interactive build system
README.md                  - This documentation
```

## Troubleshooting

### Connection Issues
- Check firewall settings
- Verify server is listening on correct port
- Review client logs in `hvnc_client_log.txt`
- Ensure matching client and server profiles

### Performance Issues
- **Profile Selection**: Choose appropriate profile for your use case
- **High Performance**: Use for gaming or real-time applications
- **Balanced**: Use for general office work and productivity
- **High Quality**: Use for design work requiring visual accuracy
- Monitor CPU and memory usage
- Check network bandwidth

### Build Issues
- Ensure Visual Studio 2019/2022 is installed
- Verify MSBuild is accessible
- Check that all source files are present
- Try cleaning and rebuilding

## Development

### Requirements
- Visual Studio 2019/2022 with C++ support
- Windows SDK
- MSBuild (automatically detected)

### Adding New Profiles
1. Edit `common/CompileTimeConfig.h`
2. Add new `#elif defined(HVNC_NEW_PROFILE)` section
3. Update `build.bat` with new profile option
4. Test compilation and functionality

### Modifying Performance Settings
All performance settings are compile-time constants in `CompileTimeConfig.h`.
No runtime configuration is supported by design for maximum optimization.

## License
Educational and research purposes only.
