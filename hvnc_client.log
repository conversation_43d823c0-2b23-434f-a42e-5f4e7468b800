[2025-07-21 09:46:09.349] [INFO] HVNC Client logging initialized
[2025-07-21 09:46:09.349] [INFO] HVNC Client starting - Host: ***************, Port: 4043
[2025-07-21 09:46:09.349] [INFO] Hidden desktop thread started successfully
[2025-07-21 09:46:09.349] [INFO] TurboJPEG wrapper initialized successfully
[2025-07-21 09:46:09.349] [INFO] TurboJPEG image processing initialized successfully
[2025-07-21 09:46:09.365] [INFO] Capture system initialized with TurboJPEG compression
[2025-07-21 09:46:09.365] [INFO] HVNC Profile: High Quality | JPEG Quality: 90 | Frame Rate: 30 FPS | Compression: 8
[2025-07-21 09:46:09.365] [INFO] DEBUG: Compile-time constants - Quality: 90, FPS: 30, Interval: 33
[2025-07-21 09:46:09.376] [INFO] Successfully connected to ***************:4043
[2025-07-21 09:46:09.397] [INFO] Successfully connected to ***************:4043
[2025-07-21 09:46:09.413] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:09.417] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:09.417] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:09.417] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:09.479] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:09.493] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:09.493] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:09.497] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:09.540] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:09.559] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:09.559] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:09.559] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:09.604] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:09.604] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:09.604] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:09.604] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:09.670] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:09.681] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:09.683] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:09.683] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:09.735] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:09.739] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:09.740] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:09.741] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:09.792] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:09.792] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:09.792] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:09.792] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:09.862] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:09.862] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:09.872] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:09.872] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:09.920] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:09.920] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:09.920] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:09.935] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:09.983] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:09.983] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:09.994] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:09.994] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.053] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.062] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 16ms
[2025-07-21 09:46:10.062] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.062] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.120] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.124] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 15ms
[2025-07-21 09:46:10.126] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.126] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.180] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.180] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:10.180] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.180] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.246] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.246] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:10.246] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.246] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.318] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.324] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:10.324] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.324] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.374] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.377] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:10.379] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.381] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.438] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.443] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:10.443] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.443] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.503] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.508] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:10.510] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.512] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.560] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.560] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:10.560] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.576] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.624] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.624] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:10.624] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.624] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.688] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.704] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 15ms
[2025-07-21 09:46:10.704] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.704] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.761] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.761] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:10.761] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.776] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.832] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.832] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:10.840] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.842] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.888] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.888] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:10.904] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.904] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:10.952] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:10.952] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:10.967] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:10.967] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:11.016] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:11.029] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 15ms
[2025-07-21 09:46:11.032] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:11.032] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:11.079] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:11.079] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:11.079] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:11.095] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:11.152] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:11.158] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 15ms
[2025-07-21 09:46:11.158] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:11.158] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:11.222] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:11.231] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:11.232] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:11.234] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:11.281] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:11.285] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:11.287] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:11.288] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:11.329] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-21 09:46:11.329] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-21 09:46:11.342] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-21 09:46:11.342] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-21 09:46:11.345] [INFO] InputThread: Socket closed
[2025-07-21 09:46:11.345] [INFO] DesktopThread: Socket closed
[2025-07-21 09:46:11.353] [INFO] TurboJPEG wrapper cleaned up
[2025-07-21 09:46:11.356] [INFO] TurboJPEG image processing cleaned up
[2025-07-21 09:46:11.358] [INFO] Hidden desktop thread finished
[2025-07-21 09:46:11.358] [INFO] HVNC Client logging shutdown
