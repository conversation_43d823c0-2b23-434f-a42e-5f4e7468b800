#include <Windows.h>
#include <iostream>
#include <chrono>
#include "common/TurboJPEGWrapper.h"

// Simple test to verify TurboJPEG functionality
int main()
{
    std::cout << "TurboJPEG Integration Test\n";
    std::cout << "==========================\n\n";

    // Initialize TurboJPEG
    std::cout << "1. Initializing TurboJPEG...\n";
    if (!InitializeTurboJPEG()) {
        std::cout << "   FAILED: Could not initialize TurboJPEG\n";
        return 1;
    }
    std::cout << "   SUCCESS: TurboJPEG initialized\n\n";

    // Create a test wrapper instance
    std::cout << "2. Creating TurboJPEG wrapper instance...\n";
    TurboJPEGWrapper wrapper;
    if (!wrapper.Initialize()) {
        std::cout << "   FAILED: Could not initialize wrapper\n";
        CleanupTurboJPEG();
        return 1;
    }
    std::cout << "   SUCCESS: Wrapper initialized\n\n";

    // Create a simple test bitmap (100x100 red square)
    std::cout << "3. Creating test bitmap (100x100 red square)...\n";
    const int width = 100;
    const int height = 100;
    const int pixelSize = 3; // RGB
    const int dataSize = width * height * pixelSize;
    
    BYTE* testData = new BYTE[dataSize];
    for (int i = 0; i < dataSize; i += 3) {
        testData[i] = 0;     // Blue
        testData[i + 1] = 0; // Green  
        testData[i + 2] = 255; // Red (BGR format)
    }
    std::cout << "   SUCCESS: Test bitmap created (" << dataSize << " bytes)\n\n";

    // Test compression with different quality levels
    int qualities[] = {30, 60, 85, 95};
    for (int q : qualities) {
        std::cout << "4. Testing compression at quality " << q << "...\n";
        
        BYTE* jpegData = nullptr;
        unsigned long jpegSize = 0;
        
        auto start = std::chrono::high_resolution_clock::now();
        
        BOOL success = wrapper.CompressBitmap(
            testData, width, height, TJPF_BGR, q, &jpegData, &jpegSize
        );
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        if (success && jpegData && jpegSize > 0) {
            double compressionRatio = (double)jpegSize / dataSize * 100.0;
            std::cout << "   SUCCESS: Compressed " << dataSize << " bytes -> " << jpegSize << " bytes\n";
            std::cout << "   Compression ratio: " << compressionRatio << "%\n";
            std::cout << "   Compression time: " << duration.count() << " microseconds\n";
            
            // Verify JPEG magic bytes
            if (jpegData[0] == 0xFF && jpegData[1] == 0xD8 && jpegData[2] == 0xFF) {
                std::cout << "   SUCCESS: Valid JPEG magic bytes detected\n";
            } else {
                std::cout << "   WARNING: Invalid JPEG magic bytes\n";
            }
            
            TurboJPEG_FreeBuffer(jpegData);
        } else {
            std::cout << "   FAILED: Compression failed\n";
        }
        std::cout << "\n";
    }

    // Test network framing function
    std::cout << "5. Testing network framing protocol...\n";
    BYTE* jpegData = nullptr;
    unsigned long jpegSize = 0;
    
    if (wrapper.CompressBitmap(testData, width, height, TJPF_BGR, 85, &jpegData, &jpegSize)) {
        // Simulate network transmission by creating a buffer with 4-byte prefix
        BYTE* networkBuffer = new BYTE[4 + jpegSize];
        
        // Write 4-byte length prefix (little-endian)
        *((DWORD*)networkBuffer) = jpegSize;
        
        // Copy JPEG data
        memcpy(networkBuffer + 4, jpegData, jpegSize);
        
        // Verify the prefix
        DWORD receivedSize = *((DWORD*)networkBuffer);
        if (receivedSize == jpegSize) {
            std::cout << "   SUCCESS: Network framing protocol works correctly\n";
            std::cout << "   Length prefix: " << receivedSize << " bytes\n";
            std::cout << "   Total network packet: " << (4 + jpegSize) << " bytes\n";
        } else {
            std::cout << "   FAILED: Network framing protocol error\n";
        }
        
        delete[] networkBuffer;
        TurboJPEG_FreeBuffer(jpegData);
    } else {
        std::cout << "   FAILED: Could not create test JPEG for network framing\n";
    }
    std::cout << "\n";

    // Display performance statistics
    std::cout << "6. Performance Statistics:\n";
    const auto& stats = wrapper.GetStats();
    std::cout << "   Total compressions: " << stats.totalCompressions << "\n";
    std::cout << "   Total input bytes: " << stats.totalInputBytes << "\n";
    std::cout << "   Total output bytes: " << stats.totalOutputBytes << "\n";
    std::cout << "   Average compression ratio: " << (stats.averageCompressionRatio * 100.0) << "%\n";
    std::cout << "   Average compression time: " << stats.averageCompressionTime << " ms\n\n";

    // Cleanup
    std::cout << "7. Cleaning up...\n";
    delete[] testData;
    CleanupTurboJPEG();
    std::cout << "   SUCCESS: Cleanup completed\n\n";

    std::cout << "TurboJPEG Integration Test COMPLETED SUCCESSFULLY!\n";
    std::cout << "The HVNC system is now ready to use TurboJPEG compression.\n\n";
    
    std::cout << "Press Enter to exit...";
    std::cin.get();
    
    return 0;
}
